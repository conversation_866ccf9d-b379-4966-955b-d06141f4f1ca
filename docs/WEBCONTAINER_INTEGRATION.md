# WebContainer Integration

This document explains the WebContainer integration in the PixiGenerator frontend application.

## Overview

WebContainer API allows running Node.js environments directly in the browser, enabling live preview of generated code without requiring external servers or sandboxes.

## Features

- **Live Code Preview**: Generated code runs in a real browser environment
- **Multiple Framework Support**: Supports React, Next.js, and Vite projects
- **Real-time Updates**: Code changes are reflected immediately in the preview
- **Isolated Environment**: Each preview runs in its own secure container
- **Dependency Management**: Automatic npm install and dev server startup

## Architecture

### Components

1. **`useWebContainer` Hook** (`src/hooks/useWebContainer.ts`)
   - Manages WebContainer instance lifecycle
   - Handles file system operations
   - Manages development server processes
   - Provides loading states and error handling

2. **`WebContainerPreview` Component** (`src/components/molecules/WebContainerPreview.tsx`)
   - UI component for the live preview
   - Integrates with the useWebContainer hook
   - Provides start/stop/refresh controls
   - Displays loading states and errors

3. **WebContainer Utilities** (`src/utils/webcontainer.ts`)
   - Framework detection from generated code
   - File system structure creation
   - Package.json generation for different frameworks
   - Configuration file templates

### Integration Points

- **CodePreviewPanel**: The main integration point where WebContainer preview replaces the placeholder
- **Next.js Configuration**: COOP/COEP headers configured for WebContainer requirements
- **Generation Metadata**: Framework information passed to WebContainer for proper setup

## Usage

### Basic Usage

The WebContainer preview is automatically available in the code preview panel when viewing generated code:

1. Navigate to a generation page (`/generations/[id]`)
2. Switch to the "Preview" tab
3. Click "Start Preview" to boot the WebContainer
4. Wait for dependencies to install and dev server to start
5. View the live preview in the iframe

### Supported Frameworks

- **React (Vite)**: Default framework, uses Vite dev server
- **Next.js**: Supports Next.js applications with proper routing
- **Auto-detection**: Framework is detected from code imports and patterns

### Framework Detection

The system automatically detects the framework based on:
- Metadata from generation (if available)
- Import statements in the code
- Code patterns and structure

## Configuration

### Next.js Headers

WebContainer requires specific headers for cross-origin isolation:

```typescript
// next.config.ts
async headers() {
  return [
    {
      source: '/generations/:path*',
      headers: [
        {
          key: 'Cross-Origin-Embedder-Policy',
          value: 'require-corp',
        },
        {
          key: 'Cross-Origin-Opener-Policy',
          value: 'same-origin',
        },
      ],
    },
  ];
}
```

### Browser Requirements

- Modern browsers with SharedArrayBuffer support
- HTTPS in production (localhost exempt in development)
- Cross-origin isolation enabled

## File System Structure

### React (Vite) Projects

```
project/
├── package.json
├── vite.config.ts
├── tsconfig.json
├── index.html
└── src/
    ├── main.tsx
    └── App.tsx (generated code)
```

### Next.js Projects

```
project/
├── package.json
├── next.config.js
├── tsconfig.json
└── src/
    └── pages/
        └── index.tsx (generated code)
```

## Error Handling

The integration includes comprehensive error handling:

- **Boot Errors**: WebContainer initialization failures
- **Mount Errors**: File system mounting issues
- **Install Errors**: npm dependency installation failures
- **Server Errors**: Development server startup problems

## Performance Considerations

- **Single Instance**: Only one WebContainer instance per session
- **Lazy Loading**: WebContainer boots only when preview is requested
- **Resource Cleanup**: Proper cleanup on component unmount
- **Caching**: File system changes are incremental when possible

## Limitations

- **Single Container**: Only one WebContainer instance can run at a time
- **Browser Support**: Requires modern browsers with SharedArrayBuffer
- **Memory Usage**: WebContainer instances consume significant memory
- **Network Dependencies**: Requires internet connection for npm packages

## Troubleshooting

### Common Issues

1. **"WebContainer not ready" Error**
   - Ensure headers are properly configured
   - Check browser compatibility
   - Verify HTTPS in production

2. **"npm install failed" Error**
   - Check network connectivity
   - Verify package.json validity
   - Try refreshing the preview

3. **Preview Not Loading**
   - Check browser console for errors
   - Verify iframe sandbox permissions
   - Ensure dev server started successfully

### Debug Mode

Enable debug logging by setting localStorage:

```javascript
localStorage.setItem('webcontainer-debug', 'true');
```

## Future Enhancements

- **Multi-file Support**: Handle multiple generated files
- **Custom Dependencies**: Allow adding custom npm packages
- **Preview Persistence**: Maintain preview state across navigation
- **Performance Optimization**: Improve boot and install times
- **Advanced Framework Support**: Add support for Vue, Svelte, etc.

## Security

- **Sandboxed Environment**: Code runs in isolated WebContainer
- **No File System Access**: Limited to WebContainer virtual file system
- **Network Isolation**: Controlled network access
- **CSP Compliance**: Compatible with Content Security Policy

## API Reference

See the individual component and hook files for detailed API documentation:

- `useWebContainer` hook interface
- `WebContainerPreview` component props
- Utility function signatures
