import { useState, useEffect, useRef } from 'react';
import { NextPageWithLayout } from '../_app';
import { AppTemplate } from '@/components/templates';
import {
	Typo<PERSON>,
	<PERSON><PERSON>,
	Alert,
	Card,
	CardContent,
} from '@/components/atoms';
import {
	UserPlusIcon,
	EnvelopeIcon,
	UserIcon,
	EllipsisVerticalIcon,
} from '@heroicons/react/24/outline';
import { useUsers } from '@/hooks/useUsers';
import { useInvitations } from '@/hooks/useInvitations';
import { withRole } from '@/components/guards';
import { InviteUserDialog } from '@/components/templates/admin/InviteUserDialog';
import { UserManagementTable } from '@/components/templates/admin/UserManagementTable';
import { InvitationManagementTable } from '@/components/templates/admin/InvitationManagementTable';

const AdminUsersPage: NextPageWithLayout = () => {
	const [activeTab, setActiveTab] = useState<'users' | 'invitations'>('users');
	const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);
	const isMountedRef = useRef(true);

	const {
		users,
		loading: usersLoading,
		error: usersError,
		getAllUsers,
		toggleUserStatus,
		deleteUser,
	} = useUsers();

	const {
		invitations,
		loading: invitationsLoading,
		error: invitationsError,
		getAllInvitations,
		createInvitation,
		deleteInvitation,
	} = useInvitations();

	useEffect(() => {
		if (isMountedRef.current) {
			getAllUsers();
			getAllInvitations();
		}

		return () => {
			isMountedRef.current = false;
		};
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, []);

	const handleInviteUser = async (email: string) => {
		// Note: Backend doesn't accept role in invitation creation
		// Role will be set to default (USER) and can be changed later by admin
		const success = await createInvitation({ email });
		if (success) {
			setIsInviteDialogOpen(false);
		}
	};

	const handleToggleUserStatus = async (userId: string) => {
		await toggleUserStatus(userId);
	};

	const handleDeleteUser = async (userId: string) => {
		await deleteUser(userId);
	};

	const handleDeleteInvitation = async (invitationId: string) => {
		await deleteInvitation(invitationId);
	};

	const activeUsers = users.filter((u) => u.status === 'ACTIVE');
	const pendingInvitations = invitations.filter(
		(inv) => inv.status === 'PENDING',
	);

	return (
		<div className='min-h-screen bg-base'>
			<div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8'>
				{/* Header */}
				<div className='mb-8'>
					<div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
						<div>
							<Typography
								variant='h1'
								className='mb-2 text-foreground'>
								User Management
							</Typography>
							<Typography
								variant='body'
								color='secondary'>
								Manage users, invitations, and access permissions
							</Typography>
						</div>
						<Button
							variant='primary'
							size='md'
							onClick={() => setIsInviteDialogOpen(true)}
							className='inline-flex items-center gap-2 bg-primary hover:bg-primary-hover text-primary-content'>
							<UserPlusIcon className='w-4 h-4' />
							Invite User
						</Button>
					</div>
				</div>

				{/* Stats Cards */}
				<div className='grid grid-cols-1 md:grid-cols-3 gap-6 mb-8'>
					<Card className='bg-surface border border-border shadow-sm hover:shadow-md transition-shadow'>
						<CardContent className='p-6'>
							<div className='flex items-center gap-4'>
								<div className='p-3 bg-primary/10 rounded-lg border border-primary/20'>
									<UserIcon className='w-6 h-6 text-primary' />
								</div>
								<div>
									<Typography variant='h3'>{activeUsers.length}</Typography>
									<Typography variant='body-sm'>Active Users</Typography>
								</div>
							</div>
						</CardContent>
					</Card>

					<Card className='bg-surface border-border shadow-sm hover:shadow-md transition-shadow'>
						<CardContent className='p-6'>
							<div className='flex items-center gap-4'>
								<div className='p-3 bg-warning/10 rounded-lg border border-warning/20'>
									<EnvelopeIcon className='w-6 h-6 text-warning' />
								</div>
								<div>
									<Typography variant='h3'>
										{pendingInvitations.length}
									</Typography>
									<Typography variant='body-sm'>Pending Invitations</Typography>
								</div>
							</div>
						</CardContent>
					</Card>

					<Card className='bg-surface border-border shadow-sm hover:shadow-md transition-shadow'>
						<CardContent className='p-6'>
							<div className='flex items-center gap-4'>
								<div className='p-3 bg-info/10 rounded-lg border border-info/20'>
									<EllipsisVerticalIcon className='w-6 h-6 text-info' />
								</div>
								<div>
									<Typography variant='h3'>
										{users.filter((u) => u.role === 'ADMIN').length}
									</Typography>
									<Typography variant='body-sm'>Admin Users</Typography>
								</div>
							</div>
						</CardContent>
					</Card>
				</div>

				{/* Tabs */}
				<div className='border-b border-border mb-6 bg-surface rounded-t-lg'>
					<nav className='-mb-px flex space-x-8 px-6 pt-4'>
						<button
							onClick={() => setActiveTab('users')}
							className={`py-3 px-1 border-b-2 font-medium text-sm transition-colors ${
								activeTab === 'users'
									? 'border-primary text-primary bg-primary/5'
									: 'border-transparent text-secondary hover:text-foreground hover:border-border-hover'
							} rounded-t-md`}>
							<Typography
								variant='body-sm'
								weight='medium'>
								Users ({users.length})
							</Typography>
						</button>
						<button
							onClick={() => setActiveTab('invitations')}
							className={`py-3 px-1 border-b-2 font-medium text-sm transition-colors ${
								activeTab === 'invitations'
									? 'border-primary text-primary bg-primary/5'
									: 'border-transparent text-secondary hover:text-foreground hover:border-border-hover'
							} rounded-t-md`}>
							<Typography
								variant='body-sm'
								weight='medium'>
								Invitations ({invitations.length})
							</Typography>
						</button>
					</nav>
				</div>

				{/* Error Messages */}
				{usersError && (
					<Alert
						variant='negative'
						className='mb-6 bg-danger/10 border-danger/20 text-danger'>
						{usersError}
					</Alert>
				)}
				{invitationsError && (
					<Alert
						variant='negative'
						className='mb-6 bg-danger/10 border-danger/20 text-danger'>
						{invitationsError}
					</Alert>
				)}

				{/* Tab Content */}
				{activeTab === 'users' && (
					<UserManagementTable
						users={users}
						loading={usersLoading}
						onToggleStatus={handleToggleUserStatus}
						onDeleteUser={handleDeleteUser}
					/>
				)}

				{activeTab === 'invitations' && (
					<InvitationManagementTable
						invitations={invitations}
						loading={invitationsLoading}
						onDeleteInvitation={handleDeleteInvitation}
					/>
				)}

				{/* Invite User Dialog */}
				<InviteUserDialog
					isOpen={isInviteDialogOpen}
					onClose={() => setIsInviteDialogOpen(false)}
					onInvite={handleInviteUser}
				/>
			</div>
		</div>
	);
};

AdminUsersPage.getLayout = (page) => {
	return <AppTemplate>{page}</AppTemplate>;
};

// Protect this page with role-based access control
export default withRole(AdminUsersPage, {
	allowedRoles: ['ADMIN'],
	allowedStatuses: ['ACTIVE'],
});
