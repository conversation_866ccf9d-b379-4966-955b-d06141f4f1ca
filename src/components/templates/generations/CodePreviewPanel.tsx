import { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Button, MonacoEditor } from '@/components/atoms';
import {
	DocumentDuplicateIcon,
	CheckIcon,
	FolderIcon,
	DocumentIcon,
	EyeIcon,
	CodeBracketIcon,
} from '@heroicons/react/24/outline';

interface FileTab {
	id: string;
	name: string;
	type: 'file' | 'folder';
	content?: string;
	language?: string;
}

interface GenerationData {
	id: string;
	type: 'UI' | 'DOCUMENTATION';
	prompt: string;
	status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
	result: string | null;
	metadata?: {
		model?: string;
		processingTime?: number;
		tokensGenerated?: number;
		framework?: string;
		styling?: string;
		language?: string;
		error?: boolean;
		errorMessage?: string;
		files?: string[];
	} | null;
	createdAt: string;
	updatedAt: string;
	project: {
		id: string;
		name: string;
		description?: string;
		status?: 'ACTIVE' | 'INACTIVE' | 'ARCHIVED';
	};
}

interface CodePreviewPanelProps {
	generation: GenerationData;
	onCodeUpdate?: (code: string) => void;
	// Streaming props
	streamingResult?: string;
	isStreaming?: boolean;
}

export const CodePreviewPanel: React.FC<CodePreviewPanelProps> = ({
	generation,
	onCodeUpdate,
	streamingResult,
	isStreaming = false,
}) => {
	const [activeView, setActiveView] = useState<'code' | 'preview'>('code');
	const [copiedFile, setCopiedFile] = useState<string | null>(null);

	// Get the current content (streaming or final result)
	const getCurrentContent = () => {
		if (isStreaming && streamingResult) {
			return streamingResult;
		}
		return generation.result || '';
	};

	// Generate files from generation metadata or use defaults
	const files: FileTab[] = (generation.metadata?.files as string[])?.map(
		(fileName: string) => ({
			id: fileName.toLowerCase().replace(/\s+/g, '-'),
			name: fileName,
			type: 'file' as const,
			language:
				fileName.endsWith('.tsx') || fileName.endsWith('.ts')
					? 'typescript'
					: fileName.endsWith('.jsx') || fileName.endsWith('.js')
					? 'javascript'
					: fileName.endsWith('.css')
					? 'css'
					: fileName.endsWith('.html')
					? 'html'
					: fileName.endsWith('.json')
					? 'json'
					: fileName.endsWith('.md')
					? 'markdown'
					: 'text',
			content:
				getCurrentContent() ||
				`// Generated code for ${fileName}\n// This will contain the actual generated code`,
		}),
	) || [
		{
			id: 'main-file',
			name: `${generation.type.toLowerCase()}-generation.${
				generation.type === 'UI' ? 'tsx' : 'md'
			}`,
			type: 'file',
			language: generation.type === 'UI' ? 'typescript' : 'markdown',
			content:
				getCurrentContent() ||
				`// Generated ${generation.type} code\n// This will contain the actual generated code`,
		},
	];

	const [activeFile, setActiveFile] = useState<string>(
		files[0]?.id || 'main-file',
	);
	const activeFileData = files.find((file) => file.id === activeFile);

	const handleCopyCode = async (fileId: string) => {
		const file = files.find((f) => f.id === fileId);
		if (file?.content) {
			try {
				await navigator.clipboard.writeText(file.content);
				setCopiedFile(fileId);
				setTimeout(() => setCopiedFile(null), 2000);
			} catch (err) {
				console.error('Failed to copy code:', err);
			}
		}
	};

	return (
		<div className='flex h-full'>
			{/* File Explorer */}
			<div className='w-64 bg-surface-50 border-r border-border-secondary flex flex-col'>
				<div className='p-3 border-b border-border-secondary'>
					<Typography
						variant='body-sm'
						weight='semibold'
						color='secondary'>
						Files
					</Typography>
				</div>
				<div className='flex-1 overflow-y-auto'>
					<div className='p-2 space-y-1'>
						{files.map((file) => (
							<button
								key={file.id}
								onClick={() => setActiveFile(file.id)}
								className={`
									w-full flex items-center gap-2 px-3 py-2 rounded-lg text-left transition-colors duration-200
									${
										activeFile === file.id
											? 'bg-primary/10 text-primary border border-primary/20'
											: 'hover:bg-surface-100 text-foreground'
									}
								`}>
								{file.type === 'folder' ? (
									<FolderIcon className='w-4 h-4 flex-shrink-0' />
								) : (
									<DocumentIcon className='w-4 h-4 flex-shrink-0' />
								)}
								<Typography
									variant='body-sm'
									className='truncate'>
									{file.name}
								</Typography>
							</button>
						))}
					</div>
				</div>
			</div>

			{/* Code/Preview Content */}
			<div className='flex-1 flex flex-col'>
				{/* View Toggle Header */}
				<div className='flex items-center justify-between p-4 border-b border-border-secondary bg-surface-50'>
					<div className='flex items-center gap-4'>
						{/* View Toggle */}
						<div className='flex items-center bg-surface-100 rounded-lg p-1'>
							<button
								onClick={() => setActiveView('code')}
								className={`
									flex items-center gap-2 px-3 py-1.5 rounded-md text-sm font-medium transition-colors
									${
										activeView === 'code'
											? 'bg-white text-primary shadow-sm'
											: 'text-secondary hover:text-foreground'
									}
								`}>
								<CodeBracketIcon className='w-4 h-4' />
								Code
							</button>
							<button
								onClick={() => setActiveView('preview')}
								className={`
									flex items-center gap-2 px-3 py-1.5 rounded-md text-sm font-medium transition-colors
									${
										activeView === 'preview'
											? 'bg-white text-primary shadow-sm'
											: 'text-secondary hover:text-foreground'
									}
								`}>
								<EyeIcon className='w-4 h-4' />
								Preview
							</button>
						</div>

						{/* File Info */}
						{activeFileData && activeView === 'code' && (
							<div className='flex items-center gap-2'>
								<DocumentIcon className='w-4 h-4 text-muted' />
								<Typography
									variant='body-sm'
									weight='medium'>
									{activeFileData.name}
								</Typography>
								{isStreaming && (
									<div className='flex items-center gap-1.5'>
										<div className='w-2 h-2 bg-primary rounded-full animate-pulse' />
										<Typography
											variant='caption'
											color='secondary'>
											Streaming...
										</Typography>
									</div>
								)}
							</div>
						)}
					</div>

					{/* Actions */}
					{activeFileData && activeView === 'code' && (
						<Button
							variant='ghost'
							size='sm'
							onClick={() => handleCopyCode(activeFile)}
							className='flex items-center gap-2'>
							{copiedFile === activeFile ? (
								<>
									<CheckIcon className='w-4 h-4 text-success' />
									<span className='text-success'>Copied!</span>
								</>
							) : (
								<>
									<DocumentDuplicateIcon className='w-4 h-4' />
									Copy
								</>
							)}
						</Button>
					)}
				</div>

				{/* Content Area */}
				<div className='flex-1 overflow-hidden'>
					{activeView === 'code' ? (
						<div className='h-full'>
							{/* Monaco Code Editor */}
							{activeFileData ? (
								<MonacoEditor
									value={!isStreaming ? activeFileData.content : undefined}
									streamingValue={isStreaming ? getCurrentContent() : undefined}
									enableStreaming={isStreaming}
									streamingDelay={30}
									language={activeFileData.language}
									theme='vs-dark'
									height='100%'
									readOnly={true}
									onChange={(value) => onCodeUpdate?.(value || '')}
									options={{
										minimap: { enabled: true },
										fontSize: 14,
										lineHeight: 20,
										fontFamily:
											'"Fira Code", "Cascadia Code", "JetBrains Mono", Consolas, "Courier New", monospace',
										fontLigatures: true,
										wordWrap: 'on',
										automaticLayout: true,
										scrollBeyondLastLine: false,
										renderLineHighlight: 'line',
										selectOnLineNumbers: true,
										roundedSelection: false,
										readOnly: true,
										cursorStyle: 'line',
										mouseWheelZoom: true,
										smoothScrolling: true,
										cursorBlinking: 'blink',
										cursorSmoothCaretAnimation: 'on',
										renderWhitespace: 'selection',
										bracketPairColorization: { enabled: true },
										guides: {
											bracketPairs: true,
											indentation: true,
										},
										suggest: {
											showKeywords: false,
											showSnippets: false,
										},
										quickSuggestions: false,
										parameterHints: { enabled: false },
										hover: { enabled: true },
										contextmenu: true,
										folding: true,
										foldingStrategy: 'indentation',
										showFoldingControls: 'always',
									}}
									className='border-0 h-full'
								/>
							) : (
								<div className='h-full flex items-center justify-center bg-surface-50'>
									<Typography
										variant='body'
										color='secondary'>
										Select a file to view its content
									</Typography>
								</div>
							)}
						</div>
					) : (
						<div className='h-full bg-white'>
							{/* Preview Placeholder */}
							<div className='h-full flex items-center justify-center'>
								<div className='text-center p-8'>
									<div className='w-16 h-16 bg-surface-100 rounded-lg flex items-center justify-center mx-auto mb-4'>
										<svg
											className='w-8 h-8 text-muted'
											fill='none'
											stroke='currentColor'
											viewBox='0 0 24 24'>
											<path
												strokeLinecap='round'
												strokeLinejoin='round'
												strokeWidth={1.5}
												d='M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z'
											/>
										</svg>
									</div>
									<Typography
										variant='h4'
										color='secondary'
										className='mb-2'>
										Preview Coming Soon
									</Typography>
									<Typography
										variant='body'
										color='tertiary'
										className='max-w-md'>
										The live preview will be available once WebContainers
										integration is added. For now, you can view and copy the
										generated code.
									</Typography>
								</div>
							</div>
						</div>
					)}
				</div>
			</div>
		</div>
	);
};
