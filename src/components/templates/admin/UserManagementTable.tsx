import { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ge, DateDisplay } from '@/components/atoms';
import {
	EllipsisVerticalIcon,
	UserIcon,
	TrashIcon,
	CheckCircleIcon,
	XCircleIcon,
} from '@heroicons/react/24/outline';
import type { User } from '@/api/UserApi';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/react';

interface UserManagementTableProps {
	users: User[];
	loading: boolean;
	onToggleStatus: (userId: string) => Promise<void>;
	onDeleteUser: (userId: string) => Promise<void>;
}

export const UserManagementTable: React.FC<UserManagementTableProps> = ({
	users,
	loading,
	onToggleStatus,
	onDeleteUser,
}) => {
	const [actionLoading, setActionLoading] = useState<string | null>(null);

	const handleToggleStatus = async (userId: string) => {
		setActionLoading(userId);
		try {
			await onToggleStatus(userId);
		} finally {
			setActionLoading(null);
		}
	};

	const handleDeleteUser = async (userId: string) => {
		if (
			window.confirm(
				'Are you sure you want to delete this user? This action cannot be undone.',
			)
		) {
			setActionLoading(userId);
			try {
				await onDeleteUser(userId);
			} finally {
				setActionLoading(null);
			}
		}
	};

	const getStatusBadge = (status: string) => {
		switch (status) {
			case 'ACTIVE':
				return (
					<Badge
						variant='success'
						size='small'>
						Active
					</Badge>
				);
			case 'INACTIVE':
				return (
					<Badge
						variant='warning'
						size='small'>
						Inactive
					</Badge>
				);
			case 'REJECTED':
				return (
					<Badge
						variant='error'
						size='small'>
						Rejected
					</Badge>
				);
			default:
				return (
					<Badge
						variant='secondary'
						size='small'>
						{status}
					</Badge>
				);
		}
	};

	const getRoleBadge = (role: string) => {
		return (
			<Badge
				variant={role === 'ADMIN' ? 'primary' : 'secondary'}
				size='small'>
				{role}
			</Badge>
		);
	};

	if (loading) {
		return (
			<div className='bg-surface rounded-lg border border-border p-8 text-center shadow-sm'>
				<Typography
					variant='body'
					className='text-secondary'>
					Loading users...
				</Typography>
			</div>
		);
	}

	if (users.length === 0) {
		return (
			<div className='bg-surface rounded-lg border border-border p-8 text-center shadow-sm'>
				<UserIcon className='w-12 h-12 text-secondary mx-auto mb-4' />
				<Typography
					variant='h4'
					className='mb-2 text-foreground'>
					No users found
				</Typography>
				<Typography
					variant='body'
					className='text-secondary'>
					Users will appear here once they register or accept invitations.
				</Typography>
			</div>
		);
	}

	return (
		<div className='bg-surface rounded-lg border border-border overflow-hidden shadow-sm'>
			<div className='overflow-x-auto'>
				<table className='w-full'>
					<thead className='bg-surface-secondary border-b border-border'>
						<tr>
							<th className='px-6 py-4 text-left text-xs font-semibold text-muted uppercase tracking-wider'>
								User
							</th>
							<th className='px-6 py-4 text-left text-xs font-semibold text-muted uppercase tracking-wider'>
								Role
							</th>
							<th className='px-6 py-4 text-left text-xs font-semibold text-muted uppercase tracking-wider'>
								Status
							</th>
							<th className='px-6 py-4 text-left text-xs font-semibold text-muted uppercase tracking-wider'>
								Joined
							</th>
							<th className='px-6 py-4 text-right text-xs font-semibold text-muted uppercase tracking-wider'>
								Actions
							</th>
						</tr>
					</thead>
					<tbody className='divide-y divide-border bg-surface'>
						{users.map((user) => (
							<tr
								key={user.id}
								className='hover:bg-surface-secondary transition-colors'>
								<td className='px-6 py-4 whitespace-nowrap'>
									<div className='flex items-center'>
										<div className='flex-shrink-0 h-10 w-10'>
											<div className='h-10 w-10 rounded-full bg-primary/10 border border-primary/20 flex items-center justify-center'>
												<UserIcon className='w-5 h-5 text-primary' />
											</div>
										</div>
										<div className='ml-4'>
											<Typography
												variant='body-sm'
												weight='medium'
												className='text-foreground'>
												{user.fullName}
											</Typography>
											<Typography variant='caption'>{user.email}</Typography>
										</div>
									</div>
								</td>
								<td className='px-6 py-4 whitespace-nowrap'>
									{getRoleBadge(user.role || 'USER')}
								</td>
								<td className='px-6 py-4 whitespace-nowrap'>
									{getStatusBadge(user.status)}
								</td>
								<td className='px-6 py-4 whitespace-nowrap'>
									<Typography
										variant='body-sm'
										className='text-secondary'>
										{user.createdAt ? (
											<DateDisplay
												date={user.createdAt}
												format='default'
											/>
										) : (
											'—'
										)}
									</Typography>
								</td>
								<td className='px-6 py-4 whitespace-nowrap text-right'>
									<Menu
										as='div'
										className='relative inline-block text-left'>
										<MenuButton
											as={Button}
											variant='ghost'
											size='sm'
											disabled={actionLoading === user.id}
											className='inline-flex items-center'>
											<EllipsisVerticalIcon className='w-4 h-4' />
										</MenuButton>
										<MenuItems className='absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-surface shadow-lg ring-1 ring-border focus:outline-none border border-border'>
											<div className='py-1'>
												<MenuItem>
													{({ focus }) => (
														<button
															onClick={() => handleToggleStatus(user.id)}
															disabled={actionLoading === user.id}
															className={`${
																focus ? 'bg-surface-secondary' : ''
															} group flex w-full items-center px-4 py-2 text-sm text-foreground disabled:opacity-50 transition-colors`}>
															{user.status === 'ACTIVE' ? (
																<XCircleIcon className='mr-3 h-4 w-4' />
															) : user.status === 'INACTIVE' ? (
																<CheckCircleIcon className='mr-3 h-4 w-4' />
															) : (
																<CheckCircleIcon className='mr-3 h-4 w-4' />
															)}
															{user.status === 'ACTIVE'
																? 'Deactivate'
																: user.status === 'REJECTED'
																? 'Reactivate'
																: 'Activate'}
														</button>
													)}
												</MenuItem>
												<MenuItem>
													{({ focus }) => (
														<button
															onClick={() => handleDeleteUser(user.id)}
															disabled={actionLoading === user.id}
															className={`${
																focus ? 'bg-danger/10' : ''
															} group flex w-full items-center px-4 py-2 text-sm text-danger disabled:opacity-50 transition-colors`}>
															<TrashIcon className='mr-3 h-4 w-4' />
															Delete User
														</button>
													)}
												</MenuItem>
											</div>
										</MenuItems>
									</Menu>
								</td>
							</tr>
						))}
					</tbody>
				</table>
			</div>
		</div>
	);
};
