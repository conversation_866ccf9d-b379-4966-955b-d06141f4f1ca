import { useState } from 'react';
import { <PERSON>po<PERSON>, <PERSON>ton, Badge, DateDisplay } from '@/components/atoms';
import {
	EllipsisVerticalIcon,
	EnvelopeIcon,
	TrashIcon,
} from '@heroicons/react/24/outline';
import type { InvitationListItem } from '@/api/InvitationApi';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/react';

interface InvitationManagementTableProps {
	invitations: InvitationListItem[];
	loading: boolean;
	onDeleteInvitation: (invitationId: string) => Promise<void>;
}

export const InvitationManagementTable: React.FC<
	InvitationManagementTableProps
> = ({ invitations, loading, onDeleteInvitation }) => {
	const [actionLoading, setActionLoading] = useState<string | null>(null);

	const handleDeleteInvitation = async (invitationId: string) => {
		if (
			window.confirm(
				'Are you sure you want to delete this invitation? This action cannot be undone.',
			)
		) {
			setActionLoading(invitationId);
			try {
				await onDeleteInvitation(invitationId);
			} finally {
				setActionLoading(null);
			}
		}
	};

	const getStatusBadge = (status: string) => {
		switch (status) {
			case 'PENDING':
				return (
					<Badge
						variant='warning'
						size='small'>
						Pending
					</Badge>
				);
			case 'ACCEPTED':
				return (
					<Badge
						variant='success'
						size='small'>
						Accepted
					</Badge>
				);
			case 'REJECTED':
				return (
					<Badge
						variant='error'
						size='small'>
						Rejected
					</Badge>
				);
			default:
				return (
					<Badge
						variant='secondary'
						size='small'>
						{status}
					</Badge>
				);
		}
	};

	const getRoleBadge = (role: string) => {
		return (
			<Badge
				variant={role === 'ADMIN' ? 'primary' : 'secondary'}
				size='small'>
				{role}
			</Badge>
		);
	};

	const isExpired = (expiresAt: string) => {
		return new Date(expiresAt) < new Date();
	};

	if (loading) {
		return (
			<div className='bg-surface rounded-lg border border-border p-8 text-center shadow-sm'>
				<Typography
					variant='body'
					className='text-secondary'>
					Loading invitations...
				</Typography>
			</div>
		);
	}

	if (invitations.length === 0) {
		return (
			<div className='bg-surface rounded-lg border border-border p-8 text-center shadow-sm'>
				<EnvelopeIcon className='w-12 h-12 text-secondary mx-auto mb-4' />
				<Typography
					variant='h4'
					className='mb-2 text-foreground'>
					No invitations found
				</Typography>
				<Typography
					variant='body'
					className='text-secondary'>
					Invitations you send will appear here.
				</Typography>
			</div>
		);
	}

	return (
		<div className='bg-surface rounded-lg border border-border overflow-hidden shadow-sm'>
			<div className='overflow-x-auto'>
				<table className='w-full'>
					<thead className='bg-surface-secondary border-b border-border'>
						<tr>
							<th className='px-6 py-4 text-left text-xs font-semibold text-secondary uppercase tracking-wider'>
								Email
							</th>
							<th className='px-6 py-4 text-left text-xs font-semibold text-secondary uppercase tracking-wider'>
								Role
							</th>
							<th className='px-6 py-4 text-left text-xs font-semibold text-secondary uppercase tracking-wider'>
								Status
							</th>
							<th className='px-6 py-4 text-left text-xs font-semibold text-secondary uppercase tracking-wider'>
								Sent
							</th>
							<th className='px-6 py-4 text-left text-xs font-semibold text-secondary uppercase tracking-wider'>
								Expires
							</th>
							<th className='px-6 py-4 text-right text-xs font-semibold text-secondary uppercase tracking-wider'>
								Actions
							</th>
						</tr>
					</thead>
					<tbody className='divide-y divide-border bg-surface'>
						{invitations.map((invitation) => (
							<tr
								key={invitation.id}
								className={`hover:bg-surface-secondary transition-colors ${
									isExpired(invitation.expiresAt) ? 'opacity-60' : ''
								}`}>
								<td className='px-6 py-4 whitespace-nowrap'>
									<div className='flex items-center'>
										<div className='flex-shrink-0 h-8 w-8'>
											<div className='h-8 w-8 rounded-full bg-warning/10 border border-warning/20 flex items-center justify-center'>
												<EnvelopeIcon className='w-4 h-4 text-warning' />
											</div>
										</div>
										<div className='ml-3'>
											<Typography
												variant='body-sm'
												weight='medium'
												className='text-foreground'>
												{invitation.email}
											</Typography>
										</div>
									</div>
								</td>
								<td className='px-6 py-4 whitespace-nowrap'>
									{getRoleBadge(invitation.role)}
								</td>
								<td className='px-6 py-4 whitespace-nowrap'>
									<div className='flex items-center gap-2'>
										{getStatusBadge(invitation.status)}
										{isExpired(invitation.expiresAt) && (
											<Badge
												variant='error'
												size='small'>
												Expired
											</Badge>
										)}
									</div>
								</td>
								<td className='px-6 py-4 whitespace-nowrap'>
									<Typography
										variant='body-sm'
										className='text-secondary'>
										<DateDisplay
											date={invitation.createdAt}
											format='default'
										/>
									</Typography>
								</td>
								<td className='px-6 py-4 whitespace-nowrap'>
									<Typography
										variant='body-sm'
										className='text-secondary'>
										<DateDisplay
											date={invitation.expiresAt}
											format='default'
										/>
									</Typography>
								</td>
								<td className='px-6 py-4 whitespace-nowrap text-right'>
									<Menu
										as='div'
										className='relative inline-block text-left'>
										<MenuButton
											as={Button}
											variant='ghost'
											size='sm'
											disabled={actionLoading === invitation.id}
											className='inline-flex items-center'>
											<EllipsisVerticalIcon className='w-4 h-4' />
										</MenuButton>
										<MenuItems className='absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-surface shadow-lg ring-1 ring-border focus:outline-none'>
											<div className='py-1'>
												<MenuItem>
													{({ focus }) => (
														<button
															onClick={() =>
																handleDeleteInvitation(invitation.id)
															}
															disabled={actionLoading === invitation.id}
															className={`${
																focus ? 'bg-surface-50' : ''
															} group flex w-full items-center px-4 py-2 text-sm text-negative disabled:opacity-50`}>
															<TrashIcon className='mr-3 h-4 w-4' />
															Delete Invitation
														</button>
													)}
												</MenuItem>
											</div>
										</MenuItems>
									</Menu>
								</td>
							</tr>
						))}
					</tbody>
				</table>
			</div>
		</div>
	);
};
