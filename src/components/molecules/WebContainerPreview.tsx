import { useEffect, useState, useCallback } from 'react';
import { <PERSON><PERSON><PERSON>, But<PERSON> } from '@/components/atoms';
import { useWebContainer } from '@/hooks/useWebContainer';
import {
	createFileSystemFromCode,
	detectFramework,
} from '@/utils/webcontainer';
import { stripMarkdownCodeBlocks } from '@/utils/markdown';
import {
	PlayIcon,
	StopIcon,
	ArrowPathIcon,
	ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';

interface WebContainerPreviewProps {
	code: string;
	metadata?: {
		model?: string;
		processingTime?: number;
		tokensGenerated?: number;
		framework?: string;
		styling?: string;
		language?: string;
		error?: boolean;
		errorMessage?: string;
		files?: string[];
	} | null;
	className?: string;
}

export const WebContainerPreview: React.FC<WebContainerPreviewProps> = ({
	code,
	metadata,
	className = '',
}) => {
	const {
		webcontainer,
		isBooting,
		isReady,
		error,
		url,
		mountFiles,
		installDependencies,
		startDevServer,
		stopDevServer,
		isInstalling,
		isStarting,
	} = useWebContainer();

	const [isPreviewReady, setIsPreviewReady] = useState(false);
	const [previewError, setPreviewError] = useState<string | null>(null);

	// Clean the code by stripping markdown code blocks
	const cleanCode = stripMarkdownCodeBlocks(code);

	// Detect framework from code and metadata
	const framework = detectFramework(cleanCode, metadata);

	// Handle starting the preview
	const handleStartPreview = async () => {
		if (!isReady || !cleanCode.trim()) return;

		try {
			setPreviewError(null);
			setIsPreviewReady(false);

			// Create file system from generated code
			const fileSystem = createFileSystemFromCode(cleanCode, framework);

			// Mount files to WebContainer
			await mountFiles(fileSystem);

			// Install dependencies
			await installDependencies();

			// Start dev server
			await startDevServer();

			setIsPreviewReady(true);
		} catch (err) {
			const errorMessage =
				err instanceof Error ? err.message : 'Failed to start preview';
			setPreviewError(errorMessage);
		}
	};

	// Handle stopping the preview
	const handleStopPreview = () => {
		stopDevServer();
		setIsPreviewReady(false);
		setPreviewError(null);
	};

	// Handle refreshing the preview
	const handleRefreshPreview = useCallback(async () => {
		if (!isReady) return;

		try {
			setPreviewError(null);

			// Update the main file with new code
			const fileName = framework.toLowerCase().includes('next')
				? 'src/pages/index.tsx'
				: 'src/App.tsx';

			if (webcontainer) {
				await webcontainer.fs.writeFile(fileName, cleanCode);
			}
		} catch (err) {
			const errorMessage =
				err instanceof Error ? err.message : 'Failed to refresh preview';
			setPreviewError(errorMessage);
		}
	}, [isReady, framework, webcontainer, cleanCode]);

	// Auto-refresh when code changes
	useEffect(() => {
		if (isPreviewReady && cleanCode.trim()) {
			handleRefreshPreview();
		}
	}, [cleanCode, isPreviewReady, handleRefreshPreview]);

	const isLoading = isBooting || isInstalling || isStarting;
	const canStart = isReady && !isLoading && cleanCode.trim().length > 0;
	const canStop = isPreviewReady && !isLoading;

	return (
		<div className={`h-full flex flex-col ${className}`}>
			{/* Preview Controls */}
			<div className='flex items-center justify-between p-4 border-b border-border-secondary bg-surface-50'>
				<div className='flex items-center gap-3'>
					<Typography
						variant='body-sm'
						weight='semibold'
						color='secondary'>
						Live Preview
					</Typography>
					{framework && (
						<span className='px-2 py-1 text-xs font-medium bg-primary/10 text-primary rounded-md'>
							{framework}
						</span>
					)}
				</div>

				<div className='flex items-center gap-2'>
					{!isPreviewReady ? (
						<Button
							variant='primary'
							size='sm'
							onClick={handleStartPreview}
							disabled={!canStart}
							className='flex items-center gap-2'>
							{isLoading ? (
								<ArrowPathIcon className='w-4 h-4 animate-spin' />
							) : (
								<PlayIcon className='w-4 h-4' />
							)}
							{isLoading
								? isBooting
									? 'Booting...'
									: isInstalling
									? 'Installing...'
									: 'Starting...'
								: 'Start Preview'}
						</Button>
					) : (
						<>
							<Button
								variant='ghost'
								size='sm'
								onClick={handleRefreshPreview}
								disabled={isLoading}
								className='flex items-center gap-2'>
								<ArrowPathIcon className='w-4 h-4' />
								Refresh
							</Button>
							<Button
								variant='secondary'
								size='sm'
								onClick={handleStopPreview}
								disabled={!canStop}
								className='flex items-center gap-2'>
								<StopIcon className='w-4 h-4' />
								Stop
							</Button>
						</>
					)}
				</div>
			</div>

			{/* Preview Content */}
			<div className='flex-1 relative'>
				{error || previewError ? (
					<div className='h-full flex items-center justify-center p-8'>
						<div className='text-center max-w-md'>
							<div className='w-16 h-16 bg-error/10 rounded-lg flex items-center justify-center mx-auto mb-4'>
								<ExclamationTriangleIcon className='w-8 h-8 text-error' />
							</div>
							<Typography
								variant='h4'
								className='mb-2 text-error'>
								Preview Error
							</Typography>
							<Typography
								variant='body'
								color='secondary'
								className='mb-4'>
								{error || previewError}
							</Typography>
							<Button
								variant='secondary'
								size='sm'
								onClick={handleStartPreview}>
								Try Again
							</Button>
						</div>
					</div>
				) : url && isPreviewReady ? (
					<iframe
						src={url}
						className='w-full h-full border-0'
						title='Live Preview'
						sandbox='allow-scripts allow-same-origin allow-forms allow-popups allow-modals'
					/>
				) : isLoading ? (
					<div className='h-full flex items-center justify-center'>
						<div className='text-center'>
							<div className='w-16 h-16 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4'>
								<ArrowPathIcon className='w-8 h-8 text-primary animate-spin' />
							</div>
							<Typography
								variant='h4'
								color='secondary'
								className='mb-2'>
								{isBooting
									? 'Booting WebContainer...'
									: isInstalling
									? 'Installing Dependencies...'
									: 'Starting Preview...'}
							</Typography>
							<Typography
								variant='body'
								color='tertiary'>
								This may take a few moments
							</Typography>
						</div>
					</div>
				) : (
					<div className='h-full flex items-center justify-center'>
						<div className='text-center p-8'>
							<div className='w-16 h-16 bg-surface-100 rounded-lg flex items-center justify-center mx-auto mb-4'>
								<PlayIcon className='w-8 h-8 text-muted' />
							</div>
							<Typography
								variant='h4'
								color='secondary'
								className='mb-2'>
								Ready to Preview
							</Typography>
							<Typography
								variant='body'
								color='tertiary'
								className='mb-4 max-w-md'>
								Click &quot;Start Preview&quot; to run your generated code in a
								live environment.
								{!cleanCode.trim() &&
									' Generate some code first to enable the preview.'}
							</Typography>
							{cleanCode.trim() && (
								<Button
									variant='primary'
									onClick={handleStartPreview}>
									Start Preview
								</Button>
							)}
						</div>
					</div>
				)}
			</div>
		</div>
	);
};
