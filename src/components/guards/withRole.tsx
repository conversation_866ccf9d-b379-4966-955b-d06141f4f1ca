import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { NextPageWithLayout } from '@/pages/_app';
import { Typography, Button } from '@/components/atoms';
import { ShieldExclamationIcon } from '@heroicons/react/24/outline';
import { useAuthStore } from '@/providers/auth-store-provider';
import { useAuth } from '@/hooks/useAuth';

type UserRole = 'ADMIN' | 'USER';
type UserStatus = 'ACTIVE' | 'INACTIVE' | 'REJECTED';

interface RoleGuardProps {
	children: React.ReactNode;
	allowedRoles: UserRole[];
	allowedStatuses?: UserStatus[];
	fallback?: React.ReactNode;
}

interface WithRoleOptions {
	allowedRoles: UserRole[];
	allowedStatuses?: UserStatus[];
	redirectTo?: string;
	fallback?: React.ReactNode;
}

// Removed getUserFromHeaders as it's not needed with middleware-based auth

/**
 * Higher-order component that protects pages based on user roles and status
 */
export function withRole<P extends object>(
	WrappedComponent: NextPageWithLayout<P>,
	options: WithRoleOptions,
) {
	const {
		allowedRoles,
		allowedStatuses = ['ACTIVE'],
		redirectTo = '/unauthorized',
		fallback,
	} = options;

	const RoleProtectedComponent: NextPageWithLayout<P> = (props) => {
		const router = useRouter();
		const user = useAuthStore((state) => state.user);
		const isHydrated = useAuthStore((state) => state.isHydrated);
		const { logout } = useAuth();
		const [isChecking, setIsChecking] = useState(true);
		const [hasAccess, setHasAccess] = useState(false);

		useEffect(() => {
			if (!isHydrated) {
				// Wait for store to hydrate
				return;
			}

			if (!user) {
				// User is not authenticated, redirect to signin
				router.push('/signin');
				return;
			}

			// Check role and status permissions
			const hasRole = allowedRoles.includes(user.role);
			const hasStatus = allowedStatuses.includes(user.status);

			if (!hasRole || !hasStatus) {
				// User doesn't have required permissions
				// For status issues (INACTIVE/REJECTED), log out and redirect to signin
				if (
					!hasStatus &&
					(user.status === 'INACTIVE' || user.status === 'REJECTED')
				) {
					logout();
					router.push('/signin');
				} else {
					// For role issues, redirect to unauthorized page
					router.push(redirectTo);
				}
				return;
			}

			// User has access
			setHasAccess(true);
			setIsChecking(false);
		}, [user, isHydrated, router, logout]);

		// Show loading state while checking permissions or store is not hydrated
		if (!isHydrated || isChecking) {
			return (
				fallback || (
					<div className='min-h-screen flex items-center justify-center bg-base'>
						<div className='text-center'>
							<div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4'></div>
							<Typography
								variant='body'
								className='text-secondary'>
								Checking permissions...
							</Typography>
						</div>
					</div>
				)
			);
		}

		// If user doesn't have access, don't render anything (redirect will happen)
		if (!hasAccess) {
			return null;
		}

		// If middleware didn't redirect us, we have access
		if (hasAccess) {
			return <WrappedComponent {...props} />;
		}

		// Fallback unauthorized component (shouldn't reach here if middleware works correctly)
		return (
			<div className='min-h-screen flex items-center justify-center bg-base px-4'>
				<div className='max-w-md w-full text-center space-y-6'>
					<ShieldExclamationIcon className='w-16 h-16 text-warning mx-auto' />
					<div>
						<Typography
							variant='h2'
							className='mb-4 text-foreground'>
							Access Denied
						</Typography>
						<Typography
							variant='body'
							className='text-secondary mb-6'>
							You don&apos;t have permission to access this page. Required
							roles: {allowedRoles.join(', ')}
						</Typography>
					</div>
					<div className='flex flex-col sm:flex-row gap-3'>
						<Button
							variant='outline'
							size='md'
							onClick={() => router.back()}
							className='flex-1'>
							Go Back
						</Button>
						<Button
							variant='primary'
							size='md'
							onClick={() => router.push('/')}
							className='flex-1'>
							Go Home
						</Button>
					</div>
				</div>
			</div>
		);
	};

	// Copy static properties
	if (WrappedComponent.getLayout) {
		RoleProtectedComponent.getLayout = WrappedComponent.getLayout;
	}

	RoleProtectedComponent.displayName = `withRole(${
		WrappedComponent.displayName || WrappedComponent.name || 'Component'
	})`;

	return RoleProtectedComponent;
}

/**
 * Component-level role guard for protecting specific components
 */
export function RoleGuard({
	children,
	allowedRoles,
	allowedStatuses = ['ACTIVE'],
	fallback,
}: RoleGuardProps) {
	const user = useAuthStore((state) => state.user);
	const isHydrated = useAuthStore((state) => state.isHydrated);
	const { logout } = useAuth();
	const router = useRouter();

	useEffect(() => {
		if (
			isHydrated &&
			user &&
			(user.status === 'INACTIVE' || user.status === 'REJECTED')
		) {
			// Log out users with invalid status
			logout();
			router.push('/signin');
		}
	}, [user, isHydrated, logout, router]);

	// Show loading state while store is hydrating
	if (!isHydrated) {
		return (
			fallback || (
				<div className='flex items-center justify-center p-8'>
					<div className='text-center'>
						<div className='animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto mb-2'></div>
						<Typography
							variant='body-sm'
							className='text-secondary'>
							Checking permissions...
						</Typography>
					</div>
				</div>
			)
		);
	}

	// If user is not authenticated, don't render children
	if (!user) {
		return (
			fallback || (
				<div className='flex items-center justify-center p-8'>
					<div className='text-center'>
						<Typography
							variant='body-sm'
							className='text-secondary'>
							Please sign in to access this content.
						</Typography>
					</div>
				</div>
			)
		);
	}

	// Check role and status permissions
	const hasRole = allowedRoles.includes(user.role);
	const hasStatus = allowedStatuses.includes(user.status);

	if (!hasRole || !hasStatus) {
		// For status issues (INACTIVE/REJECTED), don't render anything
		// The page-level guard should handle the redirect
		if (
			!hasStatus &&
			(user.status === 'INACTIVE' || user.status === 'REJECTED')
		) {
			return null;
		}

		// For role issues, show access denied message
		return (
			fallback || (
				<div className='flex items-center justify-center p-8'>
					<div className='text-center space-y-4'>
						<ShieldExclamationIcon className='w-12 h-12 text-warning mx-auto' />
						<div>
							<Typography
								variant='h4'
								className='mb-2 text-foreground'>
								Access Denied
							</Typography>
							<Typography
								variant='body-sm'
								className='text-secondary'>
								Required roles: {allowedRoles.join(', ')}
							</Typography>
						</div>
					</div>
				</div>
			)
		);
	}

	return <>{children}</>;
}

export default withRole;
