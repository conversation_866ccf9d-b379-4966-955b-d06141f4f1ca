import { FileSystemTree } from '@/hooks/useWebContainer';

export interface GeneratedFile {
	name: string;
	content: string;
	language?: string;
}

/**
 * Creates a package.json for Vite React TypeScript projects
 * Based on the standard Vite React-TS template
 */
export const createPackageJson = (): string => {
	return JSON.stringify(
		{
			name: 'generated-preview',
			private: true,
			version: '0.0.0',
			type: 'module',
			scripts: {
				dev: 'vite',
				build: 'tsc && vite build',
				lint: 'eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0',
				preview: 'vite preview',
			},
			dependencies: {
				react: '^18.2.0',
				'react-dom': '^18.2.0',
			},
			devDependencies: {
				'@types/react': '^18.2.66',
				'@types/react-dom': '^18.2.22',
				'@typescript-eslint/eslint-plugin': '^7.2.0',
				'@typescript-eslint/parser': '^7.2.0',
				'@vitejs/plugin-react': '^4.2.1',
				eslint: '^8.57.0',
				'eslint-plugin-react-hooks': '^4.6.0',
				'eslint-plugin-react-refresh': '^0.4.6',
				typescript: '^5.2.2',
				vite: '^5.2.0',
			},
		},
		null,
		2,
	);
};

/**
 * Creates a Vite config for React TypeScript projects
 * Based on the standard Vite React-TS template
 */
export const createViteConfig = (): string => {
	return `import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    host: true
  }
})`;
};

/**
 * Creates a TypeScript config for Vite React projects
 * Based on the standard Vite React-TS template
 */
export const createTsConfig = (): string => {
	return JSON.stringify(
		{
			compilerOptions: {
				target: 'ES2020',
				useDefineForClassFields: true,
				lib: ['ES2020', 'DOM', 'DOM.Iterable'],
				module: 'ESNext',
				skipLibCheck: true,
				/* Bundler mode */
				moduleResolution: 'bundler',
				allowImportingTsExtensions: true,
				resolveJsonModule: true,
				isolatedModules: true,
				noEmit: true,
				jsx: 'react-jsx',
				/* Linting */
				strict: true,
				noUnusedLocals: true,
				noUnusedParameters: true,
				noFallthroughCasesInSwitch: true,
			},
			include: ['src'],
			references: [{ path: './tsconfig.node.json' }],
		},
		null,
		2,
	);
};

/**
 * Creates a TypeScript config for Node.js (tsconfig.node.json)
 * Required by Vite React-TS template
 */
export const createTsConfigNode = (): string => {
	return JSON.stringify(
		{
			compilerOptions: {
				composite: true,
				skipLibCheck: true,
				module: 'ESNext',
				moduleResolution: 'bundler',
				allowSyntheticDefaultImports: true,
			},
			include: ['vite.config.ts'],
		},
		null,
		2,
	);
};

/**
 * Creates an index.html file for Vite projects
 */
export const createIndexHtml = (): string => {
	return `<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Generated Preview</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>`;
};

/**
 * Creates a main.tsx file for Vite projects
 */
export const createMainTsx = (): string => {
	return `import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)`;
};

/**
 * Converts generated code into a WebContainer-compatible file system
 * Always creates a Vite React TypeScript project structure
 */
export const createFileSystemFromCode = (
	code: string,
	fileName: string = 'App.tsx',
): FileSystemTree => {
	// Always use Vite React TypeScript template structure
	return {
		'package.json': {
			file: {
				contents: createPackageJson(),
			},
		},
		'vite.config.ts': {
			file: {
				contents: createViteConfig(),
			},
		},
		'tsconfig.json': {
			file: {
				contents: createTsConfig(),
			},
		},
		'tsconfig.node.json': {
			file: {
				contents: createTsConfigNode(),
			},
		},
		'index.html': {
			file: {
				contents: createIndexHtml(),
			},
		},
		src: {
			directory: {
				'main.tsx': {
					file: {
						contents: createMainTsx(),
					},
				},
				[fileName]: {
					file: {
						contents: code,
					},
				},
			},
		},
	};
};

/**
 * Always returns 'react' since we only support Vite React TypeScript projects
 */
export const detectFramework = (
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	_code: string,
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	_metadata?: Record<string, unknown> | null,
): string => {
	// Always return 'react' since we only use Vite React-TS template
	return 'react';
};
