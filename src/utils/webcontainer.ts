import { FileSystemTree } from '@/hooks/useWebContainer';

export interface GeneratedFile {
	name: string;
	content: string;
	language?: string;
}

/**
 * Creates a basic package.json for React/Next.js projects
 */
export const createPackageJson = (framework: string = 'react'): string => {
	const basePackage = {
		name: 'generated-preview',
		version: '1.0.0',
		private: true,
		scripts: {},
		dependencies: {},
		devDependencies: {},
	};

	switch (framework.toLowerCase()) {
		case 'nextjs':
		case 'next.js':
		case 'next':
			return JSON.stringify(
				{
					...basePackage,
					scripts: {
						dev: 'next dev',
						build: 'next build',
						start: 'next start',
						lint: 'next lint',
					},
					dependencies: {
						next: '^14.0.0',
						react: '^18.0.0',
						'react-dom': '^18.0.0',
					},
					devDependencies: {
						'@types/node': '^20.0.0',
						'@types/react': '^18.0.0',
						'@types/react-dom': '^18.0.0',
						eslint: '^8.0.0',
						'eslint-config-next': '^14.0.0',
						typescript: '^5.0.0',
					},
				},
				null,
				2,
			);

		case 'vite':
		case 'react-vite':
			return JSON.stringify(
				{
					...basePackage,
					scripts: {
						dev: 'vite',
						build: 'vite build',
						preview: 'vite preview',
					},
					dependencies: {
						react: '^18.0.0',
						'react-dom': '^18.0.0',
					},
					devDependencies: {
						'@types/react': '^18.0.0',
						'@types/react-dom': '^18.0.0',
						'@vitejs/plugin-react': '^4.0.0',
						typescript: '^5.0.0',
						vite: '^5.0.0',
					},
				},
				null,
				2,
			);

		default:
		case 'react':
			return JSON.stringify(
				{
					...basePackage,
					scripts: {
						dev: 'vite',
						build: 'vite build',
						preview: 'vite preview',
					},
					dependencies: {
						react: '^18.0.0',
						'react-dom': '^18.0.0',
					},
					devDependencies: {
						'@types/react': '^18.0.0',
						'@types/react-dom': '^18.0.0',
						'@vitejs/plugin-react': '^4.0.0',
						typescript: '^5.0.0',
						vite: '^5.0.0',
					},
				},
				null,
				2,
			);
	}
};

/**
 * Creates a basic Vite config for React projects
 */
export const createViteConfig = (): string => {
	return `import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    host: true
  }
})`;
};

/**
 * Creates a basic Next.js config
 */
export const createNextConfig = (): string => {
	return `/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
}

module.exports = nextConfig`;
};

/**
 * Creates a basic TypeScript config
 */
export const createTsConfig = (framework: string = 'react'): string => {
	const baseConfig = {
		compilerOptions: {
			target: 'ES2020',
			useDefineForClassFields: true,
			lib: ['ES2020', 'DOM', 'DOM.Iterable'],
			module: 'ESNext',
			skipLibCheck: true,
			moduleResolution: 'bundler',
			allowImportingTsExtensions: true,
			resolveJsonModule: true,
			isolatedModules: true,
			noEmit: true,
			jsx: 'react-jsx',
			strict: true,
			noUnusedLocals: true,
			noUnusedParameters: true,
			noFallthroughCasesInSwitch: true,
		},
		include: ['src'],
		references: [{ path: './tsconfig.node.json' }],
	};

	if (framework.toLowerCase().includes('next')) {
		return JSON.stringify(
			{
				compilerOptions: {
					...baseConfig.compilerOptions,
					target: 'es5',
					lib: ['dom', 'dom.iterable', 'es6'],
					allowJs: true,
					skipLibCheck: true,
					strict: true,
					noEmit: true,
					esModuleInterop: true,
					module: 'esnext',
					moduleResolution: 'bundler',
					resolveJsonModule: true,
					isolatedModules: true,
					jsx: 'preserve',
					incremental: true,
					plugins: [
						{
							name: 'next',
						},
					],
					paths: {
						'@/*': ['./src/*'],
					},
				},
				include: [
					'next-env.d.ts',
					'**/*.ts',
					'**/*.tsx',
					'.next/types/**/*.ts',
				],
				exclude: ['node_modules'],
			},
			null,
			2,
		);
	}

	return JSON.stringify(baseConfig, null, 2);
};

/**
 * Creates an index.html file for Vite projects
 */
export const createIndexHtml = (): string => {
	return `<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Generated Preview</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>`;
};

/**
 * Creates a main.tsx file for Vite projects
 */
export const createMainTsx = (): string => {
	return `import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)`;
};

/**
 * Converts generated code into a WebContainer-compatible file system
 */
export const createFileSystemFromCode = (
	code: string,
	framework: string = 'react',
	fileName: string = 'App.tsx',
): FileSystemTree => {
	const isNextJs = framework.toLowerCase().includes('next');

	if (isNextJs) {
		// Next.js project structure
		return {
			'package.json': {
				file: {
					contents: createPackageJson('nextjs'),
				},
			},
			'next.config.js': {
				file: {
					contents: createNextConfig(),
				},
			},
			'tsconfig.json': {
				file: {
					contents: createTsConfig('nextjs'),
				},
			},
			src: {
				directory: {
					pages: {
						directory: {
							'index.tsx': {
								file: {
									contents: code,
								},
							},
						},
					},
				},
			},
		};
	} else {
		// Vite React project structure
		return {
			'package.json': {
				file: {
					contents: createPackageJson('vite'),
				},
			},
			'vite.config.ts': {
				file: {
					contents: createViteConfig(),
				},
			},
			'tsconfig.json': {
				file: {
					contents: createTsConfig('vite'),
				},
			},
			'index.html': {
				file: {
					contents: createIndexHtml(),
				},
			},
			src: {
				directory: {
					'main.tsx': {
						file: {
							contents: createMainTsx(),
						},
					},
					[fileName]: {
						file: {
							contents: code,
						},
					},
				},
			},
		};
	}
};

/**
 * Extracts framework information from generated code or metadata
 */
export const detectFramework = (
	code: string,
	metadata?: any | null,
): string => {
	if (metadata?.framework) {
		return metadata.framework;
	}

	// Simple detection based on imports and patterns
	if (code.includes('from "next/') || code.includes("from 'next/")) {
		return 'nextjs';
	}

	if (code.includes('export default function') && code.includes('return (')) {
		return 'react';
	}

	return 'react'; // Default fallback
};
