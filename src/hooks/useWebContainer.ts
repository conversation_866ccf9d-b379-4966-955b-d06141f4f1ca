import { useEffect, useRef, useState, useCallback } from 'react';
import { WebContainer } from '@webcontainer/api';

export interface FileSystemTree {
	[name: string]: {
		file?: {
			contents: string;
		};
		directory?: FileSystemTree;
	};
}

export interface UseWebContainerReturn {
	webcontainer: WebContainer | null;
	isBooting: boolean;
	isReady: boolean;
	error: string | null;
	url: string | null;
	mountFiles: (files: FileSystemTree) => Promise<void>;
	writeFile: (path: string, contents: string) => Promise<void>;
	installDependencies: () => Promise<void>;
	startDevServer: () => Promise<void>;
	stopDevServer: () => void;
	isInstalling: boolean;
	isStarting: boolean;
}

export const useWebContainer = (): UseWebContainerReturn => {
	const [webcontainer, setWebcontainer] = useState<WebContainer | null>(null);
	const [isBooting, setIsBooting] = useState(false);
	const [isReady, setIsReady] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [url, setUrl] = useState<string | null>(null);
	const [isInstalling, setIsInstalling] = useState(false);
	const [isStarting, setIsStarting] = useState(false);

	const devServerProcessRef = useRef<any>(null);
	const isBootingRef = useRef(false);

	// Boot WebContainer instance (only once)
	useEffect(() => {
		const bootWebContainer = async () => {
			if (isBootingRef.current || webcontainer) return;

			try {
				setIsBooting(true);
				setError(null);
				isBootingRef.current = true;

				const instance = await WebContainer.boot();
				setWebcontainer(instance);
				setIsReady(true);

				// Listen for server-ready event
				instance.on('server-ready', (port, url) => {
					setUrl(url);
				});

				// Listen for errors
				instance.on('error', (error) => {
					setError(error.message);
				});
			} catch (err) {
				setError(err instanceof Error ? err.message : 'Failed to boot WebContainer');
			} finally {
				setIsBooting(false);
			}
		};

		bootWebContainer();
	}, [webcontainer]);

	// Mount files to the WebContainer file system
	const mountFiles = useCallback(
		async (files: FileSystemTree) => {
			if (!webcontainer) {
				throw new Error('WebContainer not ready');
			}

			try {
				await webcontainer.mount(files);
			} catch (err) {
				const errorMessage = err instanceof Error ? err.message : 'Failed to mount files';
				setError(errorMessage);
				throw new Error(errorMessage);
			}
		},
		[webcontainer],
	);

	// Write a single file
	const writeFile = useCallback(
		async (path: string, contents: string) => {
			if (!webcontainer) {
				throw new Error('WebContainer not ready');
			}

			try {
				await webcontainer.fs.writeFile(path, contents);
			} catch (err) {
				const errorMessage = err instanceof Error ? err.message : 'Failed to write file';
				setError(errorMessage);
				throw new Error(errorMessage);
			}
		},
		[webcontainer],
	);

	// Install dependencies
	const installDependencies = useCallback(async () => {
		if (!webcontainer) {
			throw new Error('WebContainer not ready');
		}

		try {
			setIsInstalling(true);
			setError(null);

			const installProcess = await webcontainer.spawn('npm', ['install']);
			const exitCode = await installProcess.exit;

			if (exitCode !== 0) {
				throw new Error('npm install failed');
			}
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : 'Failed to install dependencies';
			setError(errorMessage);
			throw new Error(errorMessage);
		} finally {
			setIsInstalling(false);
		}
	}, [webcontainer]);

	// Start development server
	const startDevServer = useCallback(async () => {
		if (!webcontainer) {
			throw new Error('WebContainer not ready');
		}

		try {
			setIsStarting(true);
			setError(null);

			// Stop existing dev server if running
			if (devServerProcessRef.current) {
				devServerProcessRef.current.kill();
			}

			// Start new dev server
			const devProcess = await webcontainer.spawn('npm', ['run', 'dev']);
			devServerProcessRef.current = devProcess;
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : 'Failed to start dev server';
			setError(errorMessage);
			throw new Error(errorMessage);
		} finally {
			setIsStarting(false);
		}
	}, [webcontainer]);

	// Stop development server
	const stopDevServer = useCallback(() => {
		if (devServerProcessRef.current) {
			devServerProcessRef.current.kill();
			devServerProcessRef.current = null;
			setUrl(null);
		}
	}, []);

	// Cleanup on unmount
	useEffect(() => {
		return () => {
			if (devServerProcessRef.current) {
				devServerProcessRef.current.kill();
			}
		};
	}, []);

	return {
		webcontainer,
		isBooting,
		isReady,
		error,
		url,
		mountFiles,
		writeFile,
		installDependencies,
		startDevServer,
		stopDevServer,
		isInstalling,
		isStarting,
	};
};
